#!/bin/bash

# SadTalker Mac优化启动脚本
echo "🎭 SadTalker Mac优化版启动器"
echo "=================================="

# 设置MPS优化环境变量
echo "🔧 设置MPS优化环境变量..."
export PYTORCH_ENABLE_MPS_FALLBACK=1
export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0
export GRADIO_ANALYTICS_ENABLED=False

# 激活conda环境
echo "🐍 激活conda环境..."
source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh
conda activate sadtalker

# 检查MPS支持
echo "🔍 检查MPS支持..."
python -c "
import torch
if torch.backends.mps.is_available():
    print('✅ MPS可用，将使用Apple Silicon GPU加速')
    device = torch.device('mps')
    test = torch.randn(10, 10).to(device)
    print('✅ MPS设备测试成功')
else:
    print('❌ MPS不可用，将使用CPU')
"

echo ""
echo "🚀 启动SadTalker..."
echo "💡 优化提示:"
echo "  • batch size: 1-2 (推荐1)"
echo "  • face model resolution: 256 (更快)"
echo "  • preprocess: crop (最快)"
echo "  • 不勾选 GFPGAN enhancer (更快)"
echo ""

# 启动应用
python start_optimized.py
