#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SadTalker MPS支持检查和设备配置测试
"""

import torch
import platform
import psutil
import os

def check_system_info():
    """检查系统信息"""
    print("=" * 60)
    print("🖥️  系统信息")
    print("=" * 60)
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"处理器架构: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    print(f"Python版本: {platform.python_version()}")

    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.1f} GB")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"内存使用率: {memory.percent}%")

def check_pytorch_info():
    """检查PyTorch配置"""
    print("\n" + "=" * 60)
    print("🔥 PyTorch配置")
    print("=" * 60)
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")

    # 检查MPS支持
    mps_available = hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
    print(f"MPS可用: {mps_available}")

    if mps_available:
        print("✅ Apple Silicon GPU (MPS) 支持已启用")

        # 测试MPS设备
        try:
            device = torch.device('mps')
            test_tensor = torch.randn(1000, 1000).to(device)
            result = torch.mm(test_tensor, test_tensor.t())
            print(f"✅ MPS设备测试成功: {result.device}")
            print(f"✅ 测试矩阵大小: {result.shape}")
            del test_tensor, result  # 清理内存
        except Exception as e:
            print(f"❌ MPS设备测试失败: {e}")
    else:
        print("❌ MPS不可用，将使用CPU")

def get_optimal_device():
    """获取最优设备"""
    print("\n" + "=" * 60)
    print("⚡ 设备选择")
    print("=" * 60)

    if torch.cuda.is_available():
        device = "cuda"
        print(f"🎯 选择设备: {device} (NVIDIA GPU)")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        device = "mps"
        print(f"🎯 选择设备: {device} (Apple Silicon GPU)")
        print("🚀 将使用Metal Performance Shaders加速")
    else:
        device = "cpu"
        print(f"🎯 选择设备: {device}")
        print(f"CPU核心数: {psutil.cpu_count(logical=False)} 物理核心")
        print(f"CPU线程数: {psutil.cpu_count(logical=True)} 逻辑核心")

    return device

def test_performance():
    """测试不同设备的性能"""
    print("\n" + "=" * 60)
    print("🏃 性能测试")
    print("=" * 60)

    import time

    # 测试矩阵大小
    size = 2000

    # CPU测试
    print("测试CPU性能...")
    start_time = time.time()
    cpu_tensor = torch.randn(size, size)
    cpu_result = torch.mm(cpu_tensor, cpu_tensor.t())
    cpu_time = time.time() - start_time
    print(f"CPU时间: {cpu_time:.3f}秒")

    # MPS测试（如果可用）
    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        print("测试MPS性能...")
        try:
            start_time = time.time()
            mps_tensor = torch.randn(size, size).to('mps')
            mps_result = torch.mm(mps_tensor, mps_tensor.t())
            # 同步等待GPU完成
            torch.mps.synchronize()
            mps_time = time.time() - start_time
            print(f"MPS时间: {mps_time:.3f}秒")
            print(f"🚀 MPS加速比: {cpu_time/mps_time:.2f}x")

            # 清理GPU内存
            del mps_tensor, mps_result
            torch.mps.empty_cache()
        except Exception as e:
            print(f"❌ MPS性能测试失败: {e}")

def check_sadtalker_optimization():
    """检查SadTalker优化状态"""
    print("\n" + "=" * 60)
    print("🎭 SadTalker优化检查")
    print("=" * 60)

    # 检查gradio_demo.py是否已优化
    try:
        with open('src/gradio_demo.py', 'r') as f:
            content = f.read()
            if 'torch.backends.mps' in content:
                print("✅ gradio_demo.py 已优化支持MPS")
            else:
                print("❌ gradio_demo.py 未优化，仍使用旧的设备选择逻辑")
    except FileNotFoundError:
        print("❌ 找不到 src/gradio_demo.py 文件")

    # 检查inference.py是否已优化
    try:
        with open('inference.py', 'r') as f:
            content = f.read()
            if 'torch.backends.mps' in content:
                print("✅ inference.py 已优化支持MPS")
            else:
                print("❌ inference.py 未优化，仍使用旧的设备选择逻辑")
    except FileNotFoundError:
        print("❌ 找不到 inference.py 文件")

def recommend_settings():
    """推荐设置"""
    print("\n" + "=" * 60)
    print("💡 推荐设置")
    print("=" * 60)

    device = get_optimal_device()
    memory_gb = psutil.virtual_memory().total / (1024**3)

    if device == "mps":
        print("🎯 针对Apple Silicon优化的推荐设置:")
        print("  • 批处理大小 (batch_size): 1-2")
        print("  • 图像分辨率: 256 (快速) 或 512 (高质量)")
        print("  • 预处理模式: crop (最快)")
        print("  • 启用MPS环境变量:")
        print("    export PYTORCH_ENABLE_MPS_FALLBACK=1")
        print("    export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0")

        if memory_gb >= 16:
            print("  • 24GB内存充足，可以尝试batch_size=2")
        else:
            print("  • 建议保持batch_size=1以避免内存不足")

    elif device == "cpu":
        print("🎯 CPU模式推荐设置:")
        print("  • 批处理大小 (batch_size): 1")
        print("  • 图像分辨率: 256")
        print("  • 预处理模式: crop")
        print("  • 关闭面部增强器以提升速度")

if __name__ == "__main__":
    print("🎭 SadTalker Mac优化检查工具")

    check_system_info()
    check_pytorch_info()
    optimal_device = get_optimal_device()
    test_performance()
    check_sadtalker_optimization()
    recommend_settings()

    print("\n" + "=" * 60)
    print("✨ 检查完成！")
    print("=" * 60)
