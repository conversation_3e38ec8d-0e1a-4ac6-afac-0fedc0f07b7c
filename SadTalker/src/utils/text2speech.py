import os
import tempfile
from TTS.api import TTS

from TTS.api import TTS

class TTSTalker():
    def __init__(self) -> None:
        # 获取模型管理器
        model_manager = TTS().list_models()

        # 真正获取可用模型列表
        model_list = model_manager.list_models()
        print("可用模型:", model_list)

        # 取第一个模型（比如 xtts_v2）
        model_name = model_list[0]

        # 初始化 TTS
        self.tts = TTS(model_name)

    def say(self, text, file_path="output.wav"):
        self.tts.tts_to_file(text=text, file_path=file_path)



    def test(self, text, language='en'):

        tempf  = tempfile.NamedTemporaryFile(
                delete = False,
                suffix = ('.'+'wav'),
            )

        self.tts.tts_to_file(text, speaker=self.tts.speakers[0], language=language, file_path=tempf.name)

        return tempf.name
