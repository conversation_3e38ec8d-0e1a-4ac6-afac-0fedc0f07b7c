# 文件名：transformers_patch.py
# 作用：给新版 transformers 添加 LogitsWarper 兼容类

try:
    from transformers.generation.logits_process import LogitsProcessor

    # 定义一个假的 LogitsWarper，让老代码能 import 成功
    class LogitsWarper(LogitsProcessor):
        """Compatibility alias for old HuggingFace LogitsWarper"""
        pass

    # 注入到 transformers 模块里
    import transformers
    transformers.LogitsWarper = LogitsWarper

except Exception as e:
    print("LogitsWarper patch failed:", e)
