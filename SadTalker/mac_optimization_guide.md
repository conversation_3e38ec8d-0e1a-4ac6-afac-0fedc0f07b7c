# SadTalker Mac优化指南

## 针对M4 Mac的性能优化

### 1. 硬件加速设置

你的M4 Mac现在已经配置为使用Apple的Metal Performance Shaders (MPS)，这将显著提升性能：

- **MPS支持**：自动检测并使用Apple Silicon GPU
- **内存优化**：针对24GB内存进行了优化配置
- **批处理优化**：默认批处理大小调整为1，减少内存占用

### 2. 使用优化后的启动脚本

```bash
# 使用Mac优化版本启动
chmod +x webui_mac_optimized.sh
./webui_mac_optimized.sh
```

### 3. 性能调优建议

#### 批处理大小 (Batch Size)
- **推荐值**: 1-2 (已在界面中设置为默认值1)
- **高内存模式**: 可以尝试2-4
- **如果内存不足**: 保持为1

#### 图像分辨率
- **256x256**: 最快速度，适合测试
- **512x512**: 更高质量，但速度较慢

#### 预处理模式
- **crop**: 最快速度
- **full**: 更好效果但速度较慢
- **resize**: 平衡选择

### 4. 环境变量优化

以下环境变量已自动设置：
```bash
export PYTORCH_ENABLE_MPS_FALLBACK=1  # 启用MPS回退
export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0  # 内存管理优化
```

### 5. 依赖更新

#### PyTorch版本
- **旧版本**: 1.12.1 (不支持MPS)
- **新版本**: >=2.0.0 (支持Apple Silicon MPS)

#### 安装新版本PyTorch
```bash
# 激活虚拟环境
source venv/bin/activate

# 卸载旧版本
pip uninstall torch torchvision torchaudio

# 安装支持MPS的新版本
pip install torch>=2.0.0 torchvision torchaudio
```

### 6. 性能监控

#### 检查设备使用情况
```python
import torch
print(f"MPS available: {torch.backends.mps.is_available()}")
print(f"Current device: {torch.device('mps' if torch.backends.mps.is_available() else 'cpu')}")
```

#### 监控GPU使用率
```bash
# 在另一个终端运行
sudo powermetrics --samplers gpu_power -n 1 -i 1000
```

### 7. 故障排除

#### 如果遇到MPS错误
1. 设置回退到CPU：
```bash
export PYTORCH_ENABLE_MPS_FALLBACK=1
```

2. 或强制使用CPU：
```bash
python inference.py --cpu
```

#### 内存不足问题
1. 减少批处理大小到1
2. 使用256分辨率而不是512
3. 关闭面部增强器

#### 速度仍然很慢
1. 确认使用了MPS设备
2. 检查PyTorch版本是否>=2.0.0
3. 尝试重启应用程序

### 8. 预期性能提升

使用这些优化后，你应该看到：
- **GPU利用率**: 从接近0%提升到30-70%
- **生成速度**: 提升2-5倍
- **内存使用**: 更高效的内存管理
- **温度控制**: 更好的热管理

### 9. 进一步优化

#### 模型量化 (实验性)
```python
# 在模型加载后添加
model = model.to(torch.float16)  # 使用半精度
```

#### 编译优化 (PyTorch 2.0+)
```python
# 编译模型以获得更好性能
model = torch.compile(model)
```

### 10. 验证优化效果

运行以下命令验证优化是否生效：
```bash
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'MPS available: {torch.backends.mps.is_available() if hasattr(torch.backends, \"mps\") else False}')
if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
    print('✅ MPS优化已启用')
else:
    print('❌ MPS优化未启用')
"
```
