import os, sys
import gradio as gr
import transformers_patch
import threading
import time
from src.gradio_demo import <PERSON><PERSON><PERSON><PERSON>, get_progress


try:
    import webui  # in webui
    in_webui = True
except:
    in_webui = False


def toggle_audio_file(choice):
    if choice == False:
        return gr.update(visible=True), gr.update(visible=False)
    else:
        return gr.update(visible=False), gr.update(visible=True)
    
def ref_video_fn(path_of_ref_video):
    if path_of_ref_video is not None:
        return gr.update(value=True)
    else:
        return gr.update(value=False)

def sadtalker_with_progress(sad_talker, source_image, driven_audio, preprocess_type,
                           is_still_mode, enhancer, batch_size, size_of_image, pose_style):
    """带进度显示的SadTalker包装函数"""

    def run_generation():
        try:
            return sad_talker.test(
                source_image, driven_audio, preprocess_type,
                is_still_mode, enhancer, batch_size, size_of_image, pose_style
            )
        except Exception as e:
            print(f"生成过程出错: {e}")
            return None

    # 在后台线程中运行生成
    result_container = [None]

    def background_task():
        result_container[0] = run_generation()

    thread = threading.Thread(target=background_task)
    thread.start()

    # 等待完成并显示进度
    while thread.is_alive():
        time.sleep(0.5)  # 每0.5秒检查一次

    thread.join()
    return result_container[0]

def get_progress_info():
    """获取进度信息用于显示"""
    progress = get_progress()
    percentage = progress["current"]
    stage = progress["stage"]
    eta = progress["eta"]

    progress_bar = "█" * int(percentage // 5) + "░" * (20 - int(percentage // 5))
    return f"[{progress_bar}] {percentage}% - {stage} (预计剩余: {eta})"

def sadtalker_demo(checkpoint_path='checkpoints', config_path='src/config', warpfn=None):

    sad_talker = SadTalker(checkpoint_path, config_path, lazy_load=True)

    with gr.Blocks(analytics_enabled=False) as sadtalker_interface:
        gr.Markdown("<div align='center'> <h2> 😭 SadTalker: Learning Realistic 3D Motion Coefficients for Stylized Audio-Driven Single Image Talking Face Animation (CVPR 2023) </span> </h2> \
                    <a style='font-size:18px;color: #efefef' href='https://arxiv.org/abs/2211.12194'>Arxiv</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; \
                    <a style='font-size:18px;color: #efefef' href='https://sadtalker.github.io'>Homepage</a>  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; \
                     <a style='font-size:18px;color: #efefef' href='https://github.com/Winfredy/SadTalker'> Github </div>")

        with gr.Row().style(equal_height=False):
            with gr.Column(variant='panel'):
                with gr.Tabs(elem_id="sadtalker_source_image"):
                    with gr.TabItem('Upload image'):
                        with gr.Row():
                            source_image = gr.Image(label="Source image", source="upload", type="filepath", elem_id="img2img_image").style(width=512)

                with gr.Tabs(elem_id="sadtalker_driven_audio"):
                    with gr.TabItem('Upload OR TTS'):
                        with gr.Column(variant='panel'):
                            driven_audio = gr.Audio(label="Input audio", source="upload", type="filepath")

                        if sys.platform != 'win32' and not in_webui:
                            from src.utils.text2speech import TTSTalker
                            tts_talker = TTSTalker()
                            with gr.Column(variant='panel'):
                                input_text = gr.Textbox(label="Generating audio from text", lines=5, placeholder="please enter some text here, we genreate the audio from text using @Coqui.ai TTS.")
                                tts = gr.Button('Generate audio',elem_id="sadtalker_audio_generate", variant='primary')
                                tts.click(fn=tts_talker.test, inputs=[input_text], outputs=[driven_audio])

            with gr.Column(variant='panel'):
                with gr.Tabs(elem_id="sadtalker_checkbox"):
                    with gr.TabItem('Settings'):
                        gr.Markdown("need help? please visit our [best practice page](https://github.com/OpenTalker/SadTalker/blob/main/docs/best_practice.md) for more detials")
                        with gr.Column(variant='panel'):
                            # width = gr.Slider(minimum=64, elem_id="img2img_width", maximum=2048, step=8, label="Manually Crop Width", value=512) # img2img_width
                            # height = gr.Slider(minimum=64, elem_id="img2img_height", maximum=2048, step=8, label="Manually Crop Height", value=512) # img2img_width
                            pose_style = gr.Slider(minimum=0, maximum=46, step=1, label="Pose style", value=0) #
                            size_of_image = gr.Radio([256, 512], value=256, label='face model resolution', info="256=更快速度, 512=更高质量") #
                            preprocess_type = gr.Radio(['crop', 'resize','full', 'extcrop', 'extfull'], value='crop', label='preprocess', info="crop=最快速度, full=更好效果")
                            is_still_mode = gr.Checkbox(label="Still Mode (fewer head motion, works with preprocess `full`)")
                            # 针对Mac优化批处理大小，默认值更小以提高性能
                            batch_size = gr.Slider(label="batch size in generation", step=1, maximum=8, value=1)
                            enhancer = gr.Checkbox(label="GFPGAN as Face enhancer")
                            submit = gr.Button('Generate', elem_id="sadtalker_generate", variant='primary')

                with gr.Tabs(elem_id="sadtalker_genearted"):
                    with gr.TabItem('生成结果'):
                        # 进度显示
                        progress_text = gr.Textbox(
                            label="🚀 处理进度",
                            value="等待开始...",
                            interactive=False,
                            max_lines=1
                        )

                        # 性能监控
                        performance_text = gr.Textbox(
                            label="⚡ 性能状态",
                            value="设备: 检测中... | 内存: 检测中...",
                            interactive=False,
                            max_lines=1
                        )

                        gen_video = gr.Video(label="Generated video", format="mp4").style(width=256)

        # 创建性能监控函数
        def update_performance_info():
            import torch
            import psutil

            # 设备信息
            if torch.cuda.is_available():
                device_info = f"CUDA GPU ({torch.cuda.get_device_name()})"
                memory_info = f"GPU内存: {torch.cuda.memory_allocated()/1024**3:.1f}GB"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                device_info = "Apple Silicon MPS"
                memory_info = f"系统内存: {psutil.virtual_memory().used/1024**3:.1f}GB"
            else:
                device_info = "CPU"
                memory_info = f"系统内存: {psutil.virtual_memory().used/1024**3:.1f}GB"

            return f"设备: {device_info} | {memory_info}"

        # 页面加载时更新性能信息
        sadtalker_interface.load(
            fn=update_performance_info,
            outputs=[performance_text]
        )

        if warpfn:
            submit.click(
                        fn=warpfn(sad_talker.test),
                        inputs=[source_image,
                                driven_audio,
                                preprocess_type,
                                is_still_mode,
                                enhancer,
                                batch_size,
                                size_of_image,
                                pose_style
                                ],
                        outputs=[gen_video]
                        )
        else:
            submit.click(
                        fn=lambda *args: sadtalker_with_progress(sad_talker, *args),
                        inputs=[source_image,
                                driven_audio,
                                preprocess_type,
                                is_still_mode,
                                enhancer,
                                batch_size,
                                size_of_image,
                                pose_style
                                ],
                        outputs=[gen_video]
                        )

    return sadtalker_interface
 

if __name__ == "__main__":

    demo = sadtalker_demo()
    demo.queue()
    demo.launch()


