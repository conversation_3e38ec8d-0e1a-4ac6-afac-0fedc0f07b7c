#!/usr/bin/env bash

# SadTalker Mac优化启动脚本
# 专门针对Apple Silicon Mac (M1/M2/M3/M4) 优化

# 设置环境变量
export PYTORCH_ENABLE_MPS_FALLBACK=1
export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0

# 检查是否为Apple Silicon Mac
if [[ $(uname -m) == "arm64" ]]; then
    echo "检测到Apple Silicon Mac，启用MPS优化..."
    # 使用支持MPS的PyTorch版本
    export TORCH_COMMAND="pip install torch>=2.0.0 torchvision torchaudio"
else
    echo "非Apple Silicon Mac，使用默认配置..."
    export TORCH_COMMAND="pip install torch==1.12.1 torchvision==0.13.1 torchaudio==0.12.1"
fi

# Python可执行文件
if [[ -z "${python_cmd}" ]]; then
    python_cmd="python3"
fi

# Git可执行文件
if [[ -z "${GIT}" ]]; then
    export GIT="git"
fi

# 虚拟环境目录
if [[ -z "${venv_dir}" ]]; then
    venv_dir="venv"
fi

# 启动脚本
if [[ -z "${LAUNCH_SCRIPT}" ]]; then
    LAUNCH_SCRIPT="launcher.py"
fi

# 禁用错误报告
export ERROR_REPORTING=FALSE

# 不重新安装已存在的pip包
export PIP_IGNORE_INSTALLED=0

# 分隔符
delimiter="################################################################"

# 检查必要的程序
for preq in "${GIT}" "${python_cmd}"; do
    if ! hash "${preq}" &>/dev/null; then
        printf "\n%s\n" "${delimiter}"
        printf "\e[1m\e[31mERROR: %s 未安装，正在退出...\e[0m" "${preq}"
        printf "\n%s\n" "${delimiter}"
        exit 1
    fi
done

# 检查python3-venv
if ! "${python_cmd}" -c "import venv" &>/dev/null; then
    printf "\n%s\n" "${delimiter}"
    printf "\e[1m\e[31mERROR: python3-venv 未安装，正在退出...\e[0m"
    printf "\n%s\n" "${delimiter}"
    exit 1
fi

printf "\n%s\n" "${delimiter}"
printf "创建并激活Python虚拟环境"
printf "\n%s\n" "${delimiter}"

# 创建虚拟环境
if [[ ! -d "${venv_dir}" ]]; then
    "${python_cmd}" -m venv "${venv_dir}"
    first_launch=1
fi

# 激活虚拟环境
if [[ -f "${venv_dir}"/bin/activate ]]; then
    source "${venv_dir}"/bin/activate
else
    printf "\n%s\n" "${delimiter}"
    printf "\e[1m\e[31mERROR: 无法激活Python虚拟环境，正在退出...\e[0m"
    printf "\n%s\n" "${delimiter}"
    exit 1
fi

printf "\n%s\n" "${delimiter}"
printf "启动SadTalker (Mac优化版本)..."
printf "\n%s\n" "${delimiter}"

# 启动程序
exec "${python_cmd}" "${LAUNCH_SCRIPT}" "$@"
