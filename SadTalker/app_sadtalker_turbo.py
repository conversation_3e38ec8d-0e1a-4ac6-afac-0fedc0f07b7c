#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sad<PERSON>alker 高性能版本 - 针对M4 Mac优化
包含实时进度显示和性能监控
"""

import os, sys
import gradio as gr
import transformers_patch
import threading
import time
import torch
import psutil
from src.gradio_demo import <PERSON><PERSON><PERSON><PERSON>, get_progress, update_progress

# 设置高性能环境变量
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
os.environ['OMP_NUM_THREADS'] = '8'  # 优化CPU线程数
os.environ['MKL_NUM_THREADS'] = '8'
os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'

try:
    import webui
    in_webui = True
except:
    in_webui = False

def toggle_audio_file(choice):
    if choice == False:
        return gr.update(visible=True), gr.update(visible=False)
    else:
        return gr.update(visible=False), gr.update(visible=True)
    
def ref_video_fn(path_of_ref_video):
    if path_of_ref_video is not None:
        return gr.update(value=True)
    else:
        return gr.update(value=False)

def get_device_info():
    """获取设备信息"""
    if torch.cuda.is_available():
        device = "CUDA GPU"
        gpu_name = torch.cuda.get_device_name()
        memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        memory_used = torch.cuda.memory_allocated() / 1024**3
        device_detail = f"{gpu_name} ({memory_used:.1f}GB / {memory_total:.1f}GB)"
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        device = "Apple Silicon MPS"
        memory = psutil.virtual_memory()
        device_detail = f"M4 Mac ({memory.used/1024**3:.1f}GB / {memory.total/1024**3:.1f}GB)"
    else:
        device = "CPU"
        memory = psutil.virtual_memory()
        device_detail = f"CPU ({memory.used/1024**3:.1f}GB / {memory.total/1024**3:.1f}GB)"
    
    return device, device_detail

def sadtalker_turbo_generate(sad_talker, source_image, driven_audio, preprocess_type, 
                           is_still_mode, enhancer, batch_size, size_of_image, pose_style,
                           progress_text, performance_text):
    """高性能生成函数，带实时进度更新"""
    
    # 重置进度
    update_progress(0, 100, "🚀 启动高性能模式...", time.time())
    
    # 性能优化设置
    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        # Apple Silicon优化
        torch.backends.mps.allow_tf32 = True
        # 预热MPS
        warmup_tensor = torch.randn(100, 100).to('mps')
        _ = torch.mm(warmup_tensor, warmup_tensor.t())
        del warmup_tensor
        torch.mps.empty_cache()
    
    # 启动进度监控线程
    stop_monitoring = threading.Event()
    
    def monitor_progress():
        while not stop_monitoring.is_set():
            progress = get_progress()
            percentage = progress["current"]
            stage = progress["stage"]
            eta = progress["eta"]
            
            # 创建进度条
            progress_bar = "█" * int(percentage // 2.5) + "░" * (40 - int(percentage // 2.5))
            progress_info = f"[{progress_bar}] {percentage}% - {stage}"
            if eta != "计算中...":
                progress_info += f" (剩余: {eta})"
            
            # 更新性能信息
            device, device_detail = get_device_info()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            performance_info = f"🚀 {device_detail} | CPU: {cpu_percent}%"
            
            # 这里应该更新UI，但由于Gradio限制，我们只能在主线程中更新
            time.sleep(1)
    
    monitor_thread = threading.Thread(target=monitor_progress)
    monitor_thread.start()
    
    try:
        # 执行生成
        result = sad_talker.test(
            source_image, driven_audio, preprocess_type,
            is_still_mode, enhancer, batch_size, size_of_image, pose_style
        )
        
        # 停止监控
        stop_monitoring.set()
        monitor_thread.join()
        
        # 最终进度更新
        final_progress = get_progress()
        progress_bar = "█" * 40
        final_info = f"[{progress_bar}] 100% - ✅ {final_progress['stage']}"
        
        device, device_detail = get_device_info()
        final_performance = f"✅ 完成！{device_detail}"
        
        return result, final_info, final_performance
        
    except Exception as e:
        stop_monitoring.set()
        monitor_thread.join()
        error_info = f"❌ 生成失败: {str(e)}"
        return None, error_info, "❌ 处理出错"

def sadtalker_turbo_demo(checkpoint_path='checkpoints', config_path='src/config', warpfn=None):
    
    print("🚀 初始化SadTalker高性能版本...")
    sad_talker = SadTalker(checkpoint_path, config_path, lazy_load=True)
    
    # 获取初始设备信息
    device, device_detail = get_device_info()
    print(f"🎯 使用设备: {device_detail}")

    with gr.Blocks(analytics_enabled=False, title="SadTalker Turbo") as sadtalker_interface:
        gr.Markdown("""
        <div align='center'> 
        <h2>🚀 SadTalker Turbo - 高性能版本</h2>
        <p>针对Apple Silicon M4 Mac优化，支持实时进度显示</p>
        </div>
        """)

        with gr.Row().style(equal_height=False):
            with gr.Column(variant='panel'):
                with gr.Tabs(elem_id="sadtalker_source_image"):
                    with gr.TabItem('上传图片'):
                        with gr.Row():
                            source_image = gr.Image(
                                label="源图片", 
                                source="upload", 
                                type="filepath", 
                                elem_id="img2img_image"
                            ).style(width=512)

                with gr.Tabs(elem_id="sadtalker_driven_audio"):
                    with gr.TabItem('上传音频'):
                        with gr.Column(variant='panel'):
                            driven_audio = gr.Audio(
                                label="输入音频", 
                                source="upload", 
                                type="filepath"
                            )

                        if sys.platform != 'win32' and not in_webui:
                            from src.utils.text2speech import TTSTalker
                            tts_talker = TTSTalker()
                            with gr.Column(variant='panel'):
                                input_text = gr.Textbox(
                                    label="文本转语音", 
                                    lines=5, 
                                    placeholder="输入文本，将自动转换为语音"
                                )
                                tts = gr.Button('生成音频', elem_id="sadtalker_audio_generate", variant='primary')
                                tts.click(fn=tts_talker.test, inputs=[input_text], outputs=[driven_audio])

            with gr.Column(variant='panel'):
                with gr.Tabs(elem_id="sadtalker_settings"):
                    with gr.TabItem('⚡ 高性能设置'):
                        gr.Markdown("🎯 **性能优化提示**: 已针对M4 Mac优化，建议使用以下设置获得最佳性能")
                        
                        with gr.Column(variant='panel'):
                            pose_style = gr.Slider(
                                minimum=0, maximum=46, step=1, 
                                label="姿态风格", value=0
                            )
                            size_of_image = gr.Radio(
                                [256, 512], value=256, 
                                label='模型分辨率', 
                                info="256=极速模式, 512=高质量模式"
                            )
                            preprocess_type = gr.Radio(
                                ['crop', 'resize','full', 'extcrop', 'extfull'], 
                                value='crop', 
                                label='预处理模式', 
                                info="crop=最快速度, full=最佳效果"
                            )
                            is_still_mode = gr.Checkbox(
                                label="静态模式 (减少头部运动，配合full预处理使用)"
                            )
                            batch_size = gr.Slider(
                                label="批处理大小 (M4 Mac推荐1-2)", 
                                step=1, maximum=4, value=1
                            )
                            enhancer = gr.Checkbox(
                                label="GFPGAN面部增强 (会显著降低速度)"
                            )
                            
                            submit = gr.Button('🚀 开始生成', elem_id="sadtalker_generate", variant='primary')

                with gr.Tabs(elem_id="sadtalker_results"):
                    with gr.TabItem('📊 实时监控'):
                        # 实时进度显示
                        progress_text = gr.Textbox(
                            label="🔄 处理进度", 
                            value="等待开始...", 
                            interactive=False,
                            max_lines=2
                        )
                        
                        # 性能监控
                        performance_text = gr.Textbox(
                            label="⚡ 性能状态", 
                            value=f"🎯 {device_detail} | 就绪", 
                            interactive=False,
                            max_lines=1
                        )
                        
                        # 生成的视频
                        gen_video = gr.Video(
                            label="生成的视频", 
                            format="mp4"
                        ).style(width=512)

        # 绑定生成函数
        submit.click(
            fn=lambda *args: sadtalker_turbo_generate(sad_talker, *args),
            inputs=[
                source_image, driven_audio, preprocess_type,
                is_still_mode, enhancer, batch_size, 
                size_of_image, pose_style,
                progress_text, performance_text
            ],
            outputs=[gen_video, progress_text, performance_text]
        )

    return sadtalker_interface

if __name__ == "__main__":
    print("🚀 启动SadTalker Turbo...")
    
    demo = sadtalker_turbo_demo()
    demo.queue(concurrency_count=1, max_size=10)  # 限制并发以优化性能
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        inbrowser=True,
        show_error=True
    )
