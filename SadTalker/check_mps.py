#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MPS支持检查脚本
"""

import platform
import subprocess
import sys
import os

def check_system_info():
    """检查系统信息"""
    print("=" * 60)
    print("🖥️  系统信息")
    print("=" * 60)
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"处理器架构: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    print(f"Python版本: {platform.python_version()}")

def check_conda_env():
    """检查conda环境"""
    print("\n" + "=" * 60)
    print("🐍 Conda环境检查")
    print("=" * 60)
    
    try:
        result = subprocess.run(['conda', 'info', '--envs'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Conda环境列表:")
            print(result.stdout)
        else:
            print("❌ 无法获取conda环境信息")
    except FileNotFoundError:
        print("❌ conda命令未找到")

def check_pytorch_in_env():
    """在conda环境中检查PyTorch"""
    print("\n" + "=" * 60)
    print("🔥 PyTorch检查 (在sadtalker环境中)")
    print("=" * 60)
    
    # 构建conda activate命令
    conda_cmd = [
        'bash', '-c', 
        'source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh && '
        'conda activate sadtalker && '
        'python -c "'
        'import torch; '
        'print(f\"PyTorch版本: {torch.__version__}\"); '
        'print(f\"CUDA可用: {torch.cuda.is_available()}\"); '
        'mps_available = hasattr(torch.backends, \"mps\") and torch.backends.mps.is_available(); '
        'print(f\"MPS可用: {mps_available}\"); '
        'if mps_available: '
        '    print(\"✅ Apple Silicon GPU (MPS) 支持已启用\"); '
        '    device = torch.device(\"mps\"); '
        '    x = torch.randn(100, 100).to(device); '
        '    print(f\"✅ MPS设备测试成功: {x.device}\"); '
        'else: '
        '    print(\"❌ MPS不可用，将使用CPU\"); '
        '"'
    ]
    
    try:
        result = subprocess.run(conda_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ PyTorch检查失败:")
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print("❌ 检查超时")
    except Exception as e:
        print(f"❌ 检查出错: {e}")

def check_file_modifications():
    """检查文件是否已修改"""
    print("\n" + "=" * 60)
    print("📝 文件修改检查")
    print("=" * 60)
    
    files_to_check = [
        ('src/gradio_demo.py', 'torch.backends.mps'),
        ('inference.py', 'torch.backends.mps'),
        ('app_sadtalker.py', 'batch size in generation'),
    ]
    
    for file_path, search_text in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if search_text in content:
                        print(f"✅ {file_path} - 已包含优化代码")
                    else:
                        print(f"❌ {file_path} - 未找到优化代码")
            except Exception as e:
                print(f"❌ {file_path} - 读取失败: {e}")
        else:
            print(f"❌ {file_path} - 文件不存在")

def show_optimization_commands():
    """显示优化命令"""
    print("\n" + "=" * 60)
    print("🚀 优化命令")
    print("=" * 60)
    
    print("1. 激活conda环境并启动SadTalker:")
    print("   source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh")
    print("   conda activate sadtalker")
    print("   python app_sadtalker.py")
    
    print("\n2. 设置MPS环境变量 (可选):")
    print("   export PYTORCH_ENABLE_MPS_FALLBACK=1")
    print("   export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0")
    
    print("\n3. 推荐的界面设置:")
    print("   • batch size: 1 (默认)")
    print("   • face model resolution: 256 (更快)")
    print("   • preprocess: crop (最快)")
    print("   • 不勾选 GFPGAN enhancer (更快)")

def create_launch_script():
    """创建启动脚本"""
    print("\n" + "=" * 60)
    print("📜 创建启动脚本")
    print("=" * 60)
    
    script_content = '''#!/bin/bash
# SadTalker Mac优化启动脚本

echo "🎭 启动SadTalker (Mac优化版)"

# 设置MPS环境变量
export PYTORCH_ENABLE_MPS_FALLBACK=1
export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0

# 激活conda环境
source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh
conda activate sadtalker

# 检查MPS支持
echo "检查MPS支持..."
python -c "
import torch
if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
    print('✅ MPS可用，将使用Apple Silicon GPU加速')
else:
    print('❌ MPS不可用，将使用CPU')
"

# 启动应用
echo "启动SadTalker..."
python app_sadtalker.py
'''
    
    try:
        with open('launch_mac.sh', 'w') as f:
            f.write(script_content)
        os.chmod('launch_mac.sh', 0o755)
        print("✅ 已创建 launch_mac.sh 启动脚本")
        print("使用方法: ./launch_mac.sh")
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")

if __name__ == "__main__":
    print("🎭 SadTalker Mac优化检查工具")
    
    check_system_info()
    check_conda_env()
    check_pytorch_in_env()
    check_file_modifications()
    show_optimization_commands()
    create_launch_script()
    
    print("\n" + "=" * 60)
    print("✨ 检查完成！")
    print("=" * 60)
