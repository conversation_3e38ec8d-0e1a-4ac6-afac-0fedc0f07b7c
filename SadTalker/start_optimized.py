#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SadTalker Mac优化启动脚本
"""

import os
import torch

def setup_environment():
    """设置优化环境变量"""
    print("🔧 设置MPS优化环境变量...")
    
    # MPS优化环境变量
    os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
    os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
    
    # 禁用一些可能影响性能的功能
    os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'
    
    print("✅ 环境变量设置完成")

def check_device():
    """检查并显示当前设备"""
    print("\n🔍 检查设备状态...")
    
    if torch.backends.mps.is_available():
        device = torch.device('mps')
        print(f"✅ 使用设备: {device} (Apple Silicon GPU)")
        
        # 测试MPS设备
        try:
            test_tensor = torch.randn(100, 100).to(device)
            print(f"✅ MPS设备测试成功")
            del test_tensor
            torch.mps.empty_cache()
        except Exception as e:
            print(f"⚠️ MPS测试警告: {e}")
            
    elif torch.cuda.is_available():
        print("✅ 使用设备: CUDA GPU")
    else:
        print("⚠️ 使用设备: CPU (性能较慢)")

def start_sadtalker():
    """启动SadTalker"""
    print("\n🎭 启动SadTalker...")
    
    try:
        from app_sadtalker import sadtalker_demo
        
        # 创建demo实例
        demo = sadtalker_demo()
        
        print("✅ SadTalker已启动")
        print("🌐 打开浏览器访问: http://localhost:7860")
        print("\n💡 优化建议:")
        print("  • batch size: 1-2 (已优化)")
        print("  • face model resolution: 256 (更快) 或 512 (更高质量)")
        print("  • preprocess: crop (最快)")
        print("  • 不勾选 GFPGAN enhancer (更快)")
        print("\n🚀 现在你的M4 Mac将使用Apple Silicon GPU加速！")
        
        # 启动服务
        demo.queue()
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            inbrowser=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查依赖是否正确安装")

if __name__ == "__main__":
    print("🎭 SadTalker Mac优化版启动器")
    print("=" * 50)
    
    setup_environment()
    check_device()
    start_sadtalker()
