#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SadTalker 性能优化器
专门针对M4 Mac进行性能调优
"""

import os
import torch
import psutil
import threading
import time
import subprocess

class PerformanceOptimizer:
    def __init__(self):
        self.device = self._detect_device()
        self.setup_environment()
        
    def _detect_device(self):
        """检测最佳设备"""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    def setup_environment(self):
        """设置优化环境变量"""
        print("🔧 设置性能优化环境...")
        
        # MPS优化
        if self.device == "mps":
            os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
            os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
            print("✅ Apple Silicon MPS优化已启用")
        
        # CPU优化
        cpu_count = psutil.cpu_count(logical=False)
        optimal_threads = min(8, cpu_count)
        os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
        os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
        os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
        
        # PyTorch优化
        torch.set_num_threads(optimal_threads)
        
        # 内存优化
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        
        # 禁用不必要的功能
        os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'
        os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
        
        print(f"✅ CPU线程优化: {optimal_threads} 线程")
        print(f"✅ 使用设备: {self.device.upper()}")
    
    def optimize_torch_settings(self):
        """优化PyTorch设置"""
        if self.device == "mps":
            # Apple Silicon优化
            torch.backends.mps.allow_tf32 = True
            print("✅ MPS TF32优化已启用")
            
        elif self.device == "cuda":
            # CUDA优化
            torch.backends.cudnn.benchmark = True
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            print("✅ CUDA优化已启用")
        
        # 通用优化
        torch.backends.cudnn.deterministic = False
        torch.backends.cudnn.enabled = True
    
    def warmup_device(self):
        """预热设备"""
        print(f"🔥 预热{self.device.upper()}设备...")
        
        try:
            if self.device == "mps":
                # MPS预热
                warmup_tensor = torch.randn(1000, 1000).to('mps')
                for _ in range(3):
                    result = torch.mm(warmup_tensor, warmup_tensor.t())
                del warmup_tensor, result
                torch.mps.empty_cache()
                print("✅ MPS设备预热完成")
                
            elif self.device == "cuda":
                # CUDA预热
                warmup_tensor = torch.randn(1000, 1000).to('cuda')
                for _ in range(3):
                    result = torch.mm(warmup_tensor, warmup_tensor.t())
                torch.cuda.synchronize()
                del warmup_tensor, result
                torch.cuda.empty_cache()
                print("✅ CUDA设备预热完成")
                
        except Exception as e:
            print(f"⚠️ 设备预热失败: {e}")
    
    def monitor_performance(self, duration=60):
        """监控性能"""
        print(f"📊 开始性能监控 ({duration}秒)...")
        
        start_time = time.time()
        cpu_usage = []
        memory_usage = []
        
        while time.time() - start_time < duration:
            cpu_usage.append(psutil.cpu_percent(interval=1))
            memory = psutil.virtual_memory()
            memory_usage.append(memory.percent)
        
        avg_cpu = sum(cpu_usage) / len(cpu_usage)
        avg_memory = sum(memory_usage) / len(memory_usage)
        
        print(f"📊 平均CPU使用率: {avg_cpu:.1f}%")
        print(f"📊 平均内存使用率: {avg_memory:.1f}%")
        
        return avg_cpu, avg_memory
    
    def optimize_system(self):
        """系统级优化"""
        print("🔧 执行系统级优化...")
        
        try:
            # macOS特定优化
            if os.uname().sysname == 'Darwin':
                # 提高进程优先级
                os.nice(-5)
                print("✅ 进程优先级已提升")
                
                # 检查并建议系统设置
                self._check_macos_settings()
                
        except Exception as e:
            print(f"⚠️ 系统优化部分失败: {e}")
    
    def _check_macos_settings(self):
        """检查macOS设置"""
        print("🔍 检查macOS性能设置...")
        
        # 检查电源管理
        try:
            result = subprocess.run(['pmset', '-g'], capture_output=True, text=True)
            if 'powernap' in result.stdout and '1' in result.stdout:
                print("💡 建议: 关闭Power Nap以获得更好性能")
                print("   执行: sudo pmset -a powernap 0")
        except:
            pass
        
        # 检查Spotlight索引
        print("💡 建议: 将SadTalker目录添加到Spotlight隐私设置中")
    
    def get_optimal_batch_size(self):
        """计算最优批处理大小"""
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        
        if self.device == "mps":
            # Apple Silicon优化
            if total_gb >= 24:  # M4 Mac with 24GB
                return 2
            elif total_gb >= 16:
                return 1
            else:
                return 1
        elif self.device == "cuda":
            # CUDA优化
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if gpu_memory >= 8:
                return 4
            elif gpu_memory >= 6:
                return 2
            else:
                return 1
        else:
            # CPU优化
            if total_gb >= 16:
                return 2
            else:
                return 1
    
    def get_optimal_settings(self):
        """获取最优设置建议"""
        batch_size = self.get_optimal_batch_size()
        memory = psutil.virtual_memory()
        
        settings = {
            'batch_size': batch_size,
            'size': 256 if memory.total < 16 * 1024**3 else 512,
            'preprocess': 'crop',  # 最快
            'enhancer': False,  # 关闭以提升速度
            'still_mode': False
        }
        
        return settings

def apply_performance_optimizations():
    """应用所有性能优化"""
    print("🚀 启动SadTalker性能优化器...")
    
    optimizer = PerformanceOptimizer()
    optimizer.optimize_torch_settings()
    optimizer.warmup_device()
    optimizer.optimize_system()
    
    optimal_settings = optimizer.get_optimal_settings()
    
    print("\n🎯 推荐的最优设置:")
    print(f"  • 批处理大小: {optimal_settings['batch_size']}")
    print(f"  • 图像分辨率: {optimal_settings['size']}")
    print(f"  • 预处理模式: {optimal_settings['preprocess']}")
    print(f"  • 面部增强: {'开启' if optimal_settings['enhancer'] else '关闭'}")
    print(f"  • 静态模式: {'开启' if optimal_settings['still_mode'] else '关闭'}")
    
    return optimizer, optimal_settings

if __name__ == "__main__":
    optimizer, settings = apply_performance_optimizations()
    
    print("\n📊 开始性能监控...")
    optimizer.monitor_performance(30)
    
    print("\n✅ 性能优化完成！")
    print("现在可以启动SadTalker获得最佳性能。")
